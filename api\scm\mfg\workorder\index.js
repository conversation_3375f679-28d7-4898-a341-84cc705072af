import request from "../../../../utils/request";

// 生产任务相关API
export const WorkOrderApi = {
  // 查询生产任务分页
  getWorkOrderPage: async (params) => {
    return await request.get({ url: `/scm/mfg/work-order/page`, params })
  },

  // 查询生产任务详情
  getWorkOrder: async (id) => {
    return await request.get({ url: `/scm/mfg/work-order/get?id=` + id })
  },

  // 新增生产任务
  createWorkOrder: async (data) => {
    return await request.post({ url: `/scm/mfg/work-order/create`, data })
  },

  // 修改生产任务
  updateWorkOrder: async (data) => {
    return await request.put({ url: `/scm/mfg/work-order/update`, data })
  },

  // 删除生产任务
  deleteWorkOrder: async (id) => {
    return await request.delete({ url: `/scm/mfg/work-order/delete?id=` + id })
  },

  // 导出生产任务 Excel
  exportWorkOrder: async (params) => {
    return await request.download({ url: `/scm/mfg/work-order/export-excel`, params })
  },

  // ==================== 子表（任务单明细） ====================

  // 获得任务单明细列表
  getWorkOrderDetailListByBizOrderId: async (bizOrderId) => {
    return await request.get({ url: `/scm/mfg/work-order/work-order-detail/list-by-biz-order-id?bizOrderId=` + bizOrderId })
  },
}
