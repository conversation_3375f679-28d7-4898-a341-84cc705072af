import request from '@/config/axios'
import type { WorkOrderDetailVO } from '@/api/scm/mfg/workorderdetail'

// 生产任务 VO
export interface WorkOrderVO {
  id: number // 生产工单ID
  workNo: string // 生产工单编号
  planId: number // 计划ID
  customerId: number // 客户ID
  customerName: string // 客户名称
  orderType: string // 来源类型
  orderId: number // 来源ID
  orderNo: string // 来源订单编号
  requestId: number // 生产需求ID
  requestNo:string //生产需求单号
  productId: number // 产品ID
  productCode: string // 产品编号
  productName: string // 产品名称
  productSubType: string // 产品类型
  productUnit: number // 产品单位
  spec: string // 规格
  status: string   // 状态
  bomId: number // bom编号
  bomCode: string // bom编码
  bomVersion: string // 版本号
  progress: number // 完成进度
  orderDate: Date // 下单时间
  orderQuantity: number // 订单数量
  orderUnit: string // 订单单位
  deliverDate: Date // 交期
  scheduleStartDate: string // 计划开始日期
  scheduleStartTime: Date // 计划开始时间
  scheduleEndDate: string // 计划结束日期
  scheduleEndTime: Date // 计划结束时间
  scheduleQuantity: number // 计划数量
  schedulePiece: number // 计划件数
  scheduleCostTime: string // 计划用时
  scheduleLine: string // 计划产线
  scheduleHeadcount: number // 计划用人
  requirement: string // 生产要求
  remark: string // 备注
  actualLine: string // 实际生产线
  actualQuantity: number // 实际生产数量
  actualStartTime: Date // 实际开始时间
  actualEndTime: Date // 实际结束时间
  actualCostTime: string // 实际耗时
  actualHeadcount: string // 实际用人
  actualPiece: number // 实际生产件数
  actualBatchNo: string // 批号
  actualRemark: string // 生产备注
  shareImageUrl: string // 分享地址
  slotQuantity: number // 槽数
  slotCount: number // 槽数
  approveNo: string // 审批单号
  approvalStatus: string // 审批状态
  approvalTime: Date // 审批时间
  pickingStatus: number // 领料状态
  inStockStatus: number // 入库状态
  reportStatus: number // 报工状态
  qualityStatus: number // 质检状态
  feedStatus: number // 投料状态
  workOrderDetails?: WorkOrderDetailVO[] // 工单明细列表
}

// 生产任务 API
export const WorkOrderApi = {
  // 查询生产任务分页
  getWorkOrderPage: async (params: any) => {
    return await request.get({ url: `/scm/mfg/work-order/page`, params })
  },

  // 查询生产任务详情
  getWorkOrder: async (id: number) => {
    return await request.get({ url: `/scm/mfg/work-order/get?id=` + id })
  },

  // 新增生产任务
  createWorkOrder: async (data: WorkOrderVO) => {
    return await request.post({ url: `/scm/mfg/work-order/create`, data })
  },

  // 修改生产任务
  updateWorkOrder: async (data: WorkOrderVO) => {
    return await request.put({ url: `/scm/mfg/work-order/update`, data })
  },

  // 删除生产任务
  deleteWorkOrder: async (id: number) => {
    return await request.delete({ url: `/scm/mfg/work-order/delete?id=` + id })
  },

  // 导出生产任务 Excel
  exportWorkOrder: async (params) => {
    return await request.download({ url: `/scm/mfg/work-order/export-excel`, params })
  },

// ==================== 子表（任务单明细） ====================

  // 获得任务单明细列表
  getWorkOrderDetailListByBizOrderId: async (bizOrderId) => {
    return await request.get({ url: `/scm/mfg/work-order/work-order-detail/list-by-biz-order-id?bizOrderId=` + bizOrderId })
  },
}
